/**
 * Bildirim Sistemi JavaScript
 * Bu dosya, bildirim butonu ve sidebar fonksiyonalitesini yönetir.
 *
 * @package DmrLMS
 * @since 1.0.6
 */

(function() {
    'use strict';

    // DOM yüklendikten sonra çalıştır
    document.addEventListener('DOMContentLoaded', function() {
        initNotificationSystem();
    });

    // Bildirim sistemi değişkenleri
    let currentPage = 1;
    let isLoading = false;
    let hasMoreNotifications = true;

    /**
     * Bildirim sistemini başlat
     */
    function initNotificationSystem() {
        const notificationButton = document.getElementById('tutor-notification-button');
        const notificationOverlay = document.getElementById('tutor-notification-overlay');
        const notificationSidebar = document.getElementById('tutor-notification-sidebar');
        const notificationClose = document.getElementById('tutor-notification-close');

        // Elementlerin varlığını kontrol et
        if (!notificationButton || !notificationOverlay || !notificationSidebar || !notificationClose) {
            console.warn('Bildirim sistemi elementleri bulunamadı');
            return;
        }

        // Event listener'ları ekle
        setupEventListeners(notificationButton, notificationOverlay, notificationSidebar, notificationClose);

        // Klavye erişilebilirliği
        setupKeyboardAccessibility(notificationOverlay, notificationClose);

        // Bildirim sayısını yükle
        loadNotificationCount();

        console.log('Bildirim sistemi başarıyla başlatıldı');
    }

    /**
     * Event listener'ları ayarla
     */
    function setupEventListeners(button, overlay, sidebar, closeBtn) {
        // Bildirim butonuna tıklama
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleNotificationSidebar(overlay, sidebar, true);
            // Sidebar açıldığında bildirimleri yükle
            loadNotifications();
        });

        // Kapatma butonuna tıklama
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleNotificationSidebar(overlay, sidebar, false);
        });

        // Overlay'e tıklama (sidebar dışına tıklama)
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                toggleNotificationSidebar(overlay, sidebar, false);
            }
        });

        // Escape tuşu ile kapatma
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && overlay.classList.contains('active')) {
                toggleNotificationSidebar(overlay, sidebar, false);
            }
        });

        // Sayfa scroll'unu engelle (sidebar açıkken)
        overlay.addEventListener('transitionend', function() {
            if (overlay.classList.contains('active')) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        });

        // "Tümünü Okundu İşaretle" butonu
        const markAllBtn = document.getElementById('tutor-notification-mark-all-read');
        if (markAllBtn) {
            markAllBtn.addEventListener('click', function(e) {
                e.preventDefault();
                markAllNotificationsAsRead();
            });
        }

        // "Daha Fazla Yükle" butonu
        const loadMoreBtn = document.getElementById('tutor-notification-load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', function(e) {
                e.preventDefault();
                loadMoreNotifications();
            });
        }
    }

    /**
     * Klavye erişilebilirliğini ayarla
     */
    function setupKeyboardAccessibility(overlay, closeBtn) {
        // Tab tuşu ile focus yönetimi
        overlay.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                trapFocus(e, overlay);
            }
        });

        // İlk focus'u kapatma butonuna ver
        overlay.addEventListener('transitionend', function() {
            if (overlay.classList.contains('active')) {
                closeBtn.focus();
            }
        });
    }

    /**
     * Focus'u sidebar içinde tut
     */
    function trapFocus(e, container) {
        const focusableElements = container.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }

    /**
     * Bildirim sidebar'ını aç/kapat
     */
    function toggleNotificationSidebar(overlay, sidebar, show) {
        const button = document.getElementById('tutor-notification-button');

        if (show) {
            // Sidebar'ı aç
            overlay.classList.add('active');

            // Animasyon için kısa gecikme
            setTimeout(() => {
                sidebar.classList.add('active');
            }, 10);

            // Buton aktif durumunu ekle
            if (button) {
                button.classList.add('active');
            }

            // Buton animasyonu
            animateNotificationButton(true);

            // Aria durumunu güncelle
            updateAriaStates(true);

        } else {
            // Sidebar'ı kapat
            sidebar.classList.remove('active');
            overlay.classList.remove('active');

            // Buton aktif durumunu kaldır
            if (button) {
                button.classList.remove('active');
            }

            // Buton animasyonu
            animateNotificationButton(false);

            // Aria durumunu güncelle
            updateAriaStates(false);

            // Body scroll'unu geri yükle
            document.body.style.overflow = '';
        }
    }

    /**
     * Bildirim butonuna animasyon ekle
     */
    function animateNotificationButton(isOpening) {
        const button = document.getElementById('tutor-notification-button');
        if (!button) return;

        if (isOpening) {
            // Açılma animasyonu
            button.style.transform = 'scale(0.9)';
            button.style.backgroundColor = 'rgba(0, 123, 255, 0.1)';
            
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);
        } else {
            // Kapanma animasyonu
            button.style.transform = 'scale(1.1)';
            button.style.backgroundColor = '';
            
            setTimeout(() => {
                button.style.transform = 'scale(1)';
            }, 150);
        }
    }

    /**
     * ARIA durumlarını güncelle
     */
    function updateAriaStates(isOpen) {
        const button = document.getElementById('tutor-notification-button');
        const overlay = document.getElementById('tutor-notification-overlay');

        if (button) {
            button.setAttribute('aria-expanded', isOpen.toString());
            button.setAttribute('aria-label', isOpen ? 'Bildirimleri Kapat' : 'Bildirimleri Aç');
        }

        if (overlay) {
            overlay.setAttribute('aria-hidden', (!isOpen).toString());
        }
    }

    /**
     * Bildirim sayısını güncelle (gelecekte kullanılacak)
     */
    function updateNotificationCount(count) {
        const badge = document.getElementById('tutor-notification-badge');
        if (!badge) return;

        if (count > 0) {
            badge.textContent = count > 99 ? '99+' : count.toString();
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }

    /**
     * Bildirim sayısını yükle
     */
    function loadNotificationCount() {
        if (!window.dmr_notification_ajax) return;

        fetch(window.dmr_notification_ajax.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'dmr_get_notification_count',
                nonce: window.dmr_notification_ajax.nonce
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateNotificationCount(data.data.count);
            }
        })
        .catch(error => {
            console.error('Bildirim sayısı yüklenirken hata:', error);
        });
    }

    /**
     * Bildirimleri yükle
     */
    function loadNotifications(reset = true) {
        if (isLoading) return;

        if (reset) {
            currentPage = 1;
            hasMoreNotifications = true;
        }

        if (!hasMoreNotifications) return;

        isLoading = true;
        showLoading(true);

        if (!window.dmr_notification_ajax) {
            console.error('AJAX konfigürasyonu bulunamadı');
            isLoading = false;
            showLoading(false);
            return;
        }

        fetch(window.dmr_notification_ajax.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'dmr_get_notifications',
                page: currentPage,
                nonce: window.dmr_notification_ajax.nonce
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayNotifications(data.data.notifications, reset);
                updateNotificationCount(data.data.unread_count);
                hasMoreNotifications = data.data.has_more;
                updateLoadMoreButton();
                updateFooterInfo(data.data.unread_count);
            } else {
                console.error('Bildirimler yüklenirken hata:', data.data);
            }
        })
        .catch(error => {
            console.error('AJAX hatası:', error);
        })
        .finally(() => {
            isLoading = false;
            showLoading(false);
        });
    }

    /**
     * Daha fazla bildirim yükle
     */
    function loadMoreNotifications() {
        currentPage++;
        loadNotifications(false);
    }

    /**
     * Bildirimleri görüntüle
     */
    function displayNotifications(notifications, reset = true) {
        const itemsContainer = document.getElementById('tutor-notification-items');
        const emptyState = document.getElementById('tutor-notification-empty');

        if (!itemsContainer || !emptyState) return;

        if (reset) {
            itemsContainer.innerHTML = '';
        }

        if (!notifications || notifications.length === 0) {
            if (reset) {
                emptyState.style.display = 'flex';
                itemsContainer.style.display = 'none';
            }
            return;
        }

        emptyState.style.display = 'none';
        itemsContainer.style.display = 'block';

        notifications.forEach(notification => {
            const notificationElement = createNotificationElement(notification);
            itemsContainer.appendChild(notificationElement);
        });
    }

    /**
     * Bildirim elementi oluştur
     */
    function createNotificationElement(notification) {
        const div = document.createElement('div');
        div.className = `tutor-notification-item ${notification.is_read == 0 ? 'unread' : ''}`;
        div.setAttribute('data-notification-id', notification.id);

        const createdDate = new Date(notification.created_at);
        const timeAgo = getTimeAgo(createdDate);
        const courseUrl = notification.course_id ? `/course/${notification.course_id}/` : '#';

        div.innerHTML = `
            <div class="tutor-notification-item-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22,4 12,14.01 9,11.01"></polyline>
                </svg>
            </div>
            <div class="tutor-notification-item-content">
                <div class="tutor-notification-item-message">
                    ${escapeHtml(notification.message)}
                </div>
                ${notification.course_title ? `
                <div class="tutor-notification-item-course">
                    <a href="${courseUrl}" class="tutor-notification-course-link" target="_blank">
                        <svg class="tutor-notification-course-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                        </svg>
                        <span>${escapeHtml(notification.course_title)}</span>
                        <svg class="tutor-notification-external-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                            <polyline points="15,3 21,3 21,9"></polyline>
                            <line x1="10" y1="14" x2="21" y2="3"></line>
                        </svg>
                    </a>
                </div>
                ` : ''}
                <div class="tutor-notification-item-time">
                    <svg class="tutor-notification-time-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <polyline points="12,6 12,12 16,14"></polyline>
                    </svg>
                    <span class="tutor-notification-time-text" title="${createdDate.toLocaleString('tr-TR')}">
                        ${timeAgo}
                    </span>
                </div>
            </div>
            ${notification.is_read == 0 ? `
            <div class="tutor-notification-item-actions">
                <button class="tutor-notification-mark-read"
                        data-notification-id="${notification.id}"
                        title="Okundu İşaretle"
                        aria-label="Bu bildirimi okundu olarak işaretle">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polyline points="20,6 9,17 4,12"></polyline>
                    </svg>
                </button>
            </div>
            ` : ''}
        `;

        // Okundu işaretleme butonuna event listener ekle
        const markReadBtn = div.querySelector('.tutor-notification-mark-read');
        if (markReadBtn) {
            markReadBtn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                markNotificationAsRead(notification.id, div);
            });
        }

        return div;
    }

    /**
     * Bildirimi okundu olarak işaretle
     */
    function markNotificationAsRead(notificationId, element) {
        if (!window.dmr_notification_ajax) return;

        fetch(window.dmr_notification_ajax.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'dmr_mark_notification_read',
                notification_id: notificationId,
                nonce: window.dmr_notification_ajax.nonce
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Element'i okundu olarak işaretle
                element.classList.remove('unread');
                const actionsDiv = element.querySelector('.tutor-notification-item-actions');
                if (actionsDiv) {
                    actionsDiv.remove();
                }

                // Bildirim sayısını güncelle
                updateNotificationCount(data.data.unread_count);
                updateFooterInfo(data.data.unread_count);
            }
        })
        .catch(error => {
            console.error('Bildirim okundu işaretlenirken hata:', error);
        });
    }

    /**
     * Tüm bildirimleri okundu olarak işaretle
     */
    function markAllNotificationsAsRead() {
        if (!window.dmr_notification_ajax) return;

        fetch(window.dmr_notification_ajax.ajax_url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'dmr_mark_all_notifications_read',
                nonce: window.dmr_notification_ajax.nonce
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Tüm bildirimleri okundu olarak işaretle
                const unreadItems = document.querySelectorAll('.tutor-notification-item.unread');
                unreadItems.forEach(item => {
                    item.classList.remove('unread');
                    const actionsDiv = item.querySelector('.tutor-notification-item-actions');
                    if (actionsDiv) {
                        actionsDiv.remove();
                    }
                });

                // UI'yi güncelle
                updateNotificationCount(0);
                updateFooterInfo(0);
            }
        })
        .catch(error => {
            console.error('Tüm bildirimler okundu işaretlenirken hata:', error);
        });
    }

    /**
     * Yükleme durumunu göster/gizle
     */
    function showLoading(show) {
        const loading = document.getElementById('tutor-notification-loading');
        if (loading) {
            loading.style.display = show ? 'flex' : 'none';
        }
    }

    /**
     * "Daha Fazla Yükle" butonunu güncelle
     */
    function updateLoadMoreButton() {
        const loadMoreDiv = document.getElementById('tutor-notification-load-more');
        if (loadMoreDiv) {
            loadMoreDiv.style.display = hasMoreNotifications ? 'block' : 'none';
        }
    }

    /**
     * Footer bilgilerini güncelle
     */
    function updateFooterInfo(unreadCount) {
        const footerInfo = document.getElementById('tutor-notification-footer-info');
        const markAllBtn = document.getElementById('tutor-notification-mark-all-read');
        const unreadCountSpan = document.getElementById('tutor-notification-unread-count');

        if (footerInfo && unreadCountSpan) {
            unreadCountSpan.textContent = unreadCount;
            footerInfo.style.display = unreadCount > 0 ? 'block' : 'none';
        }

        if (markAllBtn) {
            markAllBtn.style.display = unreadCount > 0 ? 'flex' : 'none';
        }
    }

    /**
     * HTML escape fonksiyonu
     */
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Zaman farkını hesapla
     */
    function getTimeAgo(date) {
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) {
            return 'Az önce';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `${minutes} dakika önce`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `${hours} saat önce`;
        } else if (diffInSeconds < 2592000) {
            const days = Math.floor(diffInSeconds / 86400);
            return `${days} gün önce`;
        } else {
            return date.toLocaleDateString('tr-TR');
        }
    }

    /**
     * Global fonksiyonları window objesine ekle
     */
    window.TutorNotificationSystem = {
        updateCount: updateNotificationCount,
        loadNotifications: loadNotifications,
        loadCount: loadNotificationCount,
        toggle: function(show) {
            const overlay = document.getElementById('tutor-notification-overlay');
            const sidebar = document.getElementById('tutor-notification-sidebar');
            if (overlay && sidebar) {
                toggleNotificationSidebar(overlay, sidebar, show);
            }
        }
    };

})();
