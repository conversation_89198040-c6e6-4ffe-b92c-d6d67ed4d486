/**
 * <PERSON><PERSON><PERSON><PERSON> Sistemi CSS
 * Bu dosya, bildirim butonu ve sidebar için stil ve animasyonları içerir.
 *
 * @package DmrLMS
 * @since 1.0.6
 */

/* ===== BİLDİRİM BUTONU STİLLERİ ===== */

/* Bildirim butonu konteyneri */
.tutor-notification-toggle {
    display: flex;
    align-items: center;
    margin-right: 15px;
    position: relative;
    cursor: pointer;
}

/* Bildirim buton stili */
.tutor-notification-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--tutor-color-primary);
    position: relative;
}

.tutor-notification-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.tutor-notification-button:hover .tutor-notification-icon {
    transform: scale(1.1);
}

/* Dark mode için hover efekti */
html[data-theme="dark"] .tutor-notification-button:hover,
body.tutor-dark-mode .tutor-notification-button:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Bildirim ikonu */
.tutor-notification-icon {
    width: 24px;
    height: 24px;
    stroke: var(--tutor-color-primary);
    fill: none;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
    transition: transform 0.3s ease;
    margin-right: 0 !important; /* Orijinal Tutor LMS'den gelen margin-right'ı engelle */
}

/* Dark mode için ikon rengi */
html[data-theme="dark"] .tutor-notification-icon,
body.tutor-dark-mode .tutor-notification-icon {
    stroke: #ffffff;
}

/* Aktif durum (sidebar açıkken) */
.tutor-notification-button.active {
    background-color: rgba(0, 123, 255, 0.1);
}

html[data-theme="dark"] .tutor-notification-button.active,
body.tutor-dark-mode .tutor-notification-button.active {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Bildirim badge'i */
.tutor-notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background-color: #ff4757;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Dark mode için badge border */
html[data-theme="dark"] .tutor-notification-badge,
body.tutor-dark-mode .tutor-notification-badge {
    border-color: #0f0f0f;
}

/* ===== BİLDİRİM SIDEBAR STİLLERİ ===== */

/* Overlay */
.tutor-notification-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.tutor-notification-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Sidebar */
.tutor-notification-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100%;
    background-color: #ffffff;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    z-index: 10000;
}

.tutor-notification-overlay.active .tutor-notification-sidebar {
    right: 0;
}

/* Dark mode için sidebar */
html[data-theme="dark"] .tutor-notification-sidebar,
body.tutor-dark-mode .tutor-notification-sidebar {
    background-color: #121212;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
}

/* ===== SIDEBAR HEADER ===== */

.tutor-notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

/* Dark mode için header */
html[data-theme="dark"] .tutor-notification-header,
body.tutor-dark-mode .tutor-notification-header {
    background-color: #1e1e1e;
    border-bottom-color: #2a2a2a;
}

.tutor-notification-title {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

/* Dark mode için title */
html[data-theme="dark"] .tutor-notification-title,
body.tutor-dark-mode .tutor-notification-title {
    color: #ffffff;
}

.tutor-notification-title-icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    stroke: currentColor;
    fill: none;
    stroke-width: 2;
}

/* Kapatma butonu */
.tutor-notification-close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    background-color: transparent;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
}

.tutor-notification-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #495057;
}

/* Dark mode için close button */
html[data-theme="dark"] .tutor-notification-close,
body.tutor-dark-mode .tutor-notification-close {
    color: #ffffff;
}

html[data-theme="dark"] .tutor-notification-close:hover,
body.tutor-dark-mode .tutor-notification-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.tutor-notification-close svg {
    width: 18px;
    height: 18px;
    stroke: currentColor;
    fill: none;
}

/* ===== SIDEBAR CONTENT ===== */

.tutor-notification-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

/* Boş bildirim mesajı */
.tutor-notification-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #6c757d;
}

/* Dark mode için empty state */
html[data-theme="dark"] .tutor-notification-empty,
body.tutor-dark-mode .tutor-notification-empty {
    color: #bbbbbb;
}

.tutor-notification-empty-icon {
    width: 64px;
    height: 64px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.tutor-notification-empty-icon svg {
    width: 100%;
    height: 100%;
    stroke: currentColor;
    fill: none;
}

.tutor-notification-empty h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: inherit;
}

.tutor-notification-empty p {
    margin: 0;
    font-size: 14px;
    opacity: 0.8;
}

/* Bildirimler listesi */
.tutor-notification-list {
    padding: 0;
}

/* Yükleme animasyonu */
.tutor-notification-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #6c757d;
}

html[data-theme="dark"] .tutor-notification-loading,
body.tutor-dark-mode .tutor-notification-loading {
    color: #bbbbbb;
}

.tutor-notification-loading-spinner {
    width: 32px;
    height: 32px;
    margin-bottom: 12px;
    animation: spin 1s linear infinite;
}

.tutor-notification-loading-spinner svg {
    width: 100%;
    height: 100%;
    stroke: currentColor;
    fill: none;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Bildirim öğeleri konteyneri */
.tutor-notification-items {
    padding: 0;
}

/* Bildirim öğesi */
.tutor-notification-item {
    display: flex;
    align-items: flex-start;
    padding: 16px 20px;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
}

.tutor-notification-item:hover {
    background-color: #f8f9fa;
}

.tutor-notification-item:last-child {
    border-bottom: none;
}

/* Dark mode için bildirim öğesi */
html[data-theme="dark"] .tutor-notification-item,
body.tutor-dark-mode .tutor-notification-item {
    border-bottom-color: #2a2a2a;
}

html[data-theme="dark"] .tutor-notification-item:hover,
body.tutor-dark-mode .tutor-notification-item:hover {
    background-color: #1e1e1e;
}

/* Okunmamış bildirim */
.tutor-notification-item.unread {
    background-color: #f0f8ff;
    border-left: 4px solid #007bff;
}

html[data-theme="dark"] .tutor-notification-item.unread,
body.tutor-dark-mode .tutor-notification-item.unread {
    background-color: #1a1a2e;
    border-left-color: #4dabf7;
}

.tutor-notification-item.unread:hover {
    background-color: #e6f3ff;
}

html[data-theme="dark"] .tutor-notification-item.unread:hover,
body.tutor-dark-mode .tutor-notification-item.unread:hover {
    background-color: #252547;
}

/* Bildirim ikonu */
.tutor-notification-item-icon {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e3f2fd;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    color: #1976d2;
}

html[data-theme="dark"] .tutor-notification-item-icon,
body.tutor-dark-mode .tutor-notification-item-icon {
    background-color: #1e3a8a;
    color: #60a5fa;
}

.tutor-notification-item-icon svg {
    width: 20px;
    height: 20px;
    stroke: currentColor;
    fill: none;
}

/* Bildirim içeriği */
.tutor-notification-item-content {
    flex: 1;
    min-width: 0;
}

.tutor-notification-item-message {
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 8px;
    line-height: 1.4;
}

html[data-theme="dark"] .tutor-notification-item-message,
body.tutor-dark-mode .tutor-notification-item-message {
    color: #ffffff;
}

/* Kurs linki */
.tutor-notification-item-course {
    margin-bottom: 8px;
}

.tutor-notification-course-link {
    display: inline-flex;
    align-items: center;
    font-size: 13px;
    color: #007bff;
    text-decoration: none;
    transition: color 0.3s ease;
    max-width: 100%;
}

.tutor-notification-course-link:hover {
    color: #0056b3;
    text-decoration: none;
}

html[data-theme="dark"] .tutor-notification-course-link,
body.tutor-dark-mode .tutor-notification-course-link {
    color: #4dabf7;
}

html[data-theme="dark"] .tutor-notification-course-link:hover,
body.tutor-dark-mode .tutor-notification-course-link:hover {
    color: #74c0fc;
}

.tutor-notification-course-icon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
    flex-shrink: 0;
    stroke: currentColor;
    fill: none;
}

.tutor-notification-course-link span {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tutor-notification-external-icon {
    width: 12px;
    height: 12px;
    margin-left: 4px;
    flex-shrink: 0;
    stroke: currentColor;
    fill: none;
    opacity: 0.7;
}

/* Tarih bilgisi */
.tutor-notification-item-time {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #6c757d;
}

html[data-theme="dark"] .tutor-notification-item-time,
body.tutor-dark-mode .tutor-notification-item-time {
    color: #bbbbbb;
}

.tutor-notification-time-icon {
    width: 12px;
    height: 12px;
    margin-right: 4px;
    stroke: currentColor;
    fill: none;
    opacity: 0.7;
}

.tutor-notification-time-text {
    opacity: 0.8;
}

/* Bildirim aksiyonları */
.tutor-notification-item-actions {
    flex-shrink: 0;
    margin-left: 8px;
}

.tutor-notification-mark-read {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border: none;
    background-color: transparent;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6c757d;
}

.tutor-notification-mark-read:hover {
    background-color: #e8f5e8;
    color: #28a745;
}

html[data-theme="dark"] .tutor-notification-mark-read,
body.tutor-dark-mode .tutor-notification-mark-read {
    color: #bbbbbb;
}

html[data-theme="dark"] .tutor-notification-mark-read:hover,
body.tutor-dark-mode .tutor-notification-mark-read:hover {
    background-color: #1e3a1e;
    color: #4ade80;
}

.tutor-notification-mark-read svg {
    width: 14px;
    height: 14px;
    stroke: currentColor;
    fill: none;
}

/* Daha fazla yükle */
.tutor-notification-load-more {
    padding: 16px 20px;
    border-top: 1px solid #e9ecef;
}

html[data-theme="dark"] .tutor-notification-load-more,
body.tutor-dark-mode .tutor-notification-load-more {
    border-top-color: #2a2a2a;
}

.tutor-notification-load-more-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 10px 16px;
    border: 1px solid #dee2e6;
    background-color: #ffffff;
    color: #495057;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tutor-notification-load-more-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

html[data-theme="dark"] .tutor-notification-load-more-btn,
body.tutor-dark-mode .tutor-notification-load-more-btn {
    background-color: #2a2a2a;
    border-color: #3a3a3a;
    color: #ffffff;
}

html[data-theme="dark"] .tutor-notification-load-more-btn:hover,
body.tutor-dark-mode .tutor-notification-load-more-btn:hover {
    background-color: #3a3a3a;
    border-color: #4a4a4a;
}

.tutor-notification-load-more-btn svg {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    stroke: currentColor;
    fill: none;
}

/* Footer bilgi */
.tutor-notification-footer-info {
    margin-bottom: 10px;
    text-align: center;
}

.tutor-notification-count-text {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

html[data-theme="dark"] .tutor-notification-count-text,
body.tutor-dark-mode .tutor-notification-count-text {
    color: #bbbbbb;
}

#tutor-notification-unread-count {
    color: #007bff;
    font-weight: 600;
}

html[data-theme="dark"] #tutor-notification-unread-count,
body.tutor-dark-mode #tutor-notification-unread-count {
    color: #4dabf7;
}

/* ===== SIDEBAR FOOTER ===== */

.tutor-notification-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

/* Dark mode için footer */
html[data-theme="dark"] .tutor-notification-footer,
body.tutor-dark-mode .tutor-notification-footer {
    background-color: #1e1e1e;
    border-top-color: #2a2a2a;
}

.tutor-notification-footer-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 10px 16px;
    border: 1px solid #dee2e6;
    background-color: #ffffff;
    color: #495057;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tutor-notification-footer-btn:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
}

/* Dark mode için footer button */
html[data-theme="dark"] .tutor-notification-footer-btn,
body.tutor-dark-mode .tutor-notification-footer-btn {
    background-color: #2a2a2a;
    border-color: #3a3a3a;
    color: #ffffff;
}

html[data-theme="dark"] .tutor-notification-footer-btn:hover,
body.tutor-dark-mode .tutor-notification-footer-btn:hover {
    background-color: #3a3a3a;
    border-color: #4a4a4a;
}

.tutor-notification-footer-btn svg {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    stroke: currentColor;
    fill: none;
}

/* ===== RESPONSİVE TASARIM ===== */

/* Tablet cihazlar için */
@media (min-width: 768px) and (max-width: 1024px) {
    .tutor-notification-button:hover .tutor-notification-icon {
        transform: none;
    }
    
    .tutor-notification-sidebar {
        width: 350px;
        right: -350px;
    }
}

/* Mobil cihazlar için */
@media (max-width: 767px) {
    .tutor-notification-toggle {
        margin-right: 10px;
    }

    .tutor-notification-button {
        width: 44px;
        height: 44px;
        padding: 8px;
    }

    .tutor-notification-button:hover .tutor-notification-icon {
        transform: none;
    }

    .tutor-notification-icon {
        width: 26px;
        height: 26px;
        stroke-width: 2.2;
        margin-right: 0 !important; /* Orijinal Tutor LMS'den gelen margin-right'ı engelle */
    }

    .tutor-notification-badge {
        width: 20px;
        height: 20px;
        font-size: 12px;
        top: -3px;
        right: -3px;
    }

    .tutor-notification-sidebar {
        width: 100%;
        right: -100%;
    }

    .tutor-notification-header {
        padding: 16px;
    }

    .tutor-notification-title {
        font-size: 16px;
    }

    .tutor-notification-item {
        padding: 12px 16px;
    }

    .tutor-notification-item-icon {
        width: 36px;
        height: 36px;
        margin-right: 10px;
    }

    .tutor-notification-item-icon svg {
        width: 18px;
        height: 18px;
    }

    .tutor-notification-item-message {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .tutor-notification-course-link {
        font-size: 12px;
    }

    .tutor-notification-item-time {
        font-size: 11px;
    }

    .tutor-notification-mark-read {
        width: 24px;
        height: 24px;
    }

    .tutor-notification-mark-read svg {
        width: 12px;
        height: 12px;
    }

    .tutor-notification-load-more {
        padding: 12px 16px;
    }

    .tutor-notification-footer {
        padding: 12px 16px;
    }
}

/* Çok küçük ekranlar için */
@media (max-width: 375px) {
    .tutor-notification-button {
        width: 42px;
        height: 42px;
    }

    .tutor-notification-icon {
        width: 24px;
        height: 24px;
        margin-right: 0 !important; /* Orijinal Tutor LMS'den gelen margin-right'ı engelle */
    }

    .tutor-notification-badge {
        width: 18px;
        height: 18px;
        font-size: 11px;
    }
}
