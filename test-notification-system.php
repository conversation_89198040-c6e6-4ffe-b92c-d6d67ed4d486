<?php
/**
 * Bildirim Sistemi Test Dosyası
 * Bu dosya, bildirim sisteminin çalışıp çalışmadığını test etmek için kullanılır.
 * 
 * KULLANIM: Bu dosyayı WordPress admin panelinde çalıştırın
 * 
 * @package DmrLMS
 * @since 1.0.7
 */

// WordPress yüklü değilse çık
if (!defined('ABSPATH')) {
    // WordPress'i yükle
    require_once('../../../wp-load.php');
}

// Admin yetkisi kontrolü
if (!current_user_can('manage_options')) {
    wp_die('Bu sayfaya erişim yetkiniz yok.');
}

echo '<h1>DMR LMS Bildirim Sistemi Test</h1>';

// 1. Veritabanı tablosu kontrolü
echo '<h2>1. Veritabanı Tablosu Kontrolü</h2>';
global $wpdb;
$table_name = $wpdb->prefix . 'dmr_notifications';
$table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name;

if ($table_exists) {
    echo '<p style="color: green;">✅ Bildirim tablosu mevcut: ' . $table_name . '</p>';
    
    // Tablo yapısını göster
    $columns = $wpdb->get_results("DESCRIBE $table_name");
    echo '<h3>Tablo Yapısı:</h3>';
    echo '<table border="1" style="border-collapse: collapse;">';
    echo '<tr><th>Sütun</th><th>Tip</th><th>Null</th><th>Key</th><th>Default</th></tr>';
    foreach ($columns as $column) {
        echo '<tr>';
        echo '<td>' . $column->Field . '</td>';
        echo '<td>' . $column->Type . '</td>';
        echo '<td>' . $column->Null . '</td>';
        echo '<td>' . $column->Key . '</td>';
        echo '<td>' . $column->Default . '</td>';
        echo '</tr>';
    }
    echo '</table>';
} else {
    echo '<p style="color: red;">❌ Bildirim tablosu bulunamadı!</p>';
    echo '<p>Tabloyu oluşturmak için eklentiyi deaktif edip tekrar aktif edin.</p>';
}

// 2. Sınıf kontrolü
echo '<h2>2. Bildirim Sistemi Sınıfı Kontrolü</h2>';
if (class_exists('Dmr_LMS_Notification_System')) {
    echo '<p style="color: green;">✅ Dmr_LMS_Notification_System sınıfı yüklü</p>';
    
    $notification_system = Dmr_LMS_Notification_System::instance();
    echo '<p style="color: green;">✅ Singleton instance oluşturuldu</p>';
} else {
    echo '<p style="color: red;">❌ Dmr_LMS_Notification_System sınıfı bulunamadı!</p>';
}

// 3. Tutor LMS kontrolü
echo '<h2>3. Tutor LMS Kontrolü</h2>';
if (function_exists('tutor')) {
    echo '<p style="color: green;">✅ Tutor LMS aktif</p>';
    echo '<p>Kurs post type: ' . tutor()->course_post_type . '</p>';
} else {
    echo '<p style="color: red;">❌ Tutor LMS bulunamadı!</p>';
}

// 4. Hook kontrolü
echo '<h2>4. WordPress Hook Kontrolü</h2>';
$hooks_to_check = [
    'publish_courses',
    'wp_ajax_dmr_get_notifications',
    'wp_ajax_dmr_get_notification_count',
    'wp_ajax_dmr_mark_notification_read',
    'wp_ajax_dmr_mark_all_notifications_read'
];

foreach ($hooks_to_check as $hook) {
    if (has_action($hook)) {
        echo '<p style="color: green;">✅ Hook aktif: ' . $hook . '</p>';
    } else {
        echo '<p style="color: orange;">⚠️ Hook bulunamadı: ' . $hook . '</p>';
    }
}

// 5. Test bildirimi oluştur
echo '<h2>5. Test Bildirimi</h2>';
if (isset($_GET['create_test']) && $_GET['create_test'] == '1') {
    if (class_exists('Dmr_LMS_Notification_System') && $table_exists) {
        $notification_system = Dmr_LMS_Notification_System::instance();
        $current_user_id = get_current_user_id();
        
        // Test bildirimi oluştur
        global $wpdb;
        $result = $wpdb->insert(
            $table_name,
            array(
                'user_id' => $current_user_id,
                'course_id' => 0,
                'message' => 'Test Kursu ' . date('d.m.Y H:i') . ' tarihinde tanımlandı.',
                'created_at' => current_time('mysql'),
                'is_read' => 0
            ),
            array('%d', '%d', '%s', '%s', '%d')
        );
        
        if ($result) {
            echo '<p style="color: green;">✅ Test bildirimi oluşturuldu!</p>';
        } else {
            echo '<p style="color: red;">❌ Test bildirimi oluşturulamadı: ' . $wpdb->last_error . '</p>';
        }
    }
} else {
    echo '<p><a href="?create_test=1" style="background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;">Test Bildirimi Oluştur</a></p>';
}

// 6. Mevcut bildirimler
echo '<h2>6. Mevcut Bildirimler</h2>';
if ($table_exists) {
    $current_user_id = get_current_user_id();
    $notifications = $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_name WHERE user_id = %d ORDER BY created_at DESC LIMIT 10",
        $current_user_id
    ));
    
    if ($notifications) {
        echo '<table border="1" style="border-collapse: collapse; width: 100%;">';
        echo '<tr><th>ID</th><th>Mesaj</th><th>Tarih</th><th>Okundu</th></tr>';
        foreach ($notifications as $notification) {
            echo '<tr>';
            echo '<td>' . $notification->id . '</td>';
            echo '<td>' . esc_html($notification->message) . '</td>';
            echo '<td>' . $notification->created_at . '</td>';
            echo '<td>' . ($notification->is_read ? 'Evet' : 'Hayır') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<p>Henüz bildirim yok.</p>';
    }
}

// 7. JavaScript ve CSS dosyaları kontrolü
echo '<h2>7. Dosya Kontrolü</h2>';
$files_to_check = [
    'assets/js/notification-system.js',
    'assets/css/notification-system.css',
    'assets/php/class-notification-system.php',
    'templates/notification-button.php',
    'templates/notification-sidebar.php',
    'templates/notification-item.php'
];

foreach ($files_to_check as $file) {
    $file_path = __DIR__ . '/' . $file;
    if (file_exists($file_path)) {
        echo '<p style="color: green;">✅ Dosya mevcut: ' . $file . '</p>';
    } else {
        echo '<p style="color: red;">❌ Dosya bulunamadı: ' . $file . '</p>';
    }
}

echo '<h2>Test Tamamlandı</h2>';
echo '<p><a href="' . admin_url() . '">Admin Paneline Dön</a></p>';
?>
