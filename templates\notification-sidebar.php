<?php
/**
 * Bildirim Sidebar Template
 * Bu dosya, bildirim sidebar'ını oluşturur.
 *
 * @package DmrLMS
 * @since 1.0.6
 */

// Do<PERSON>rudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}
?>

<!-- Bildirim Sidebar Overlay -->
<div class="tutor-notification-overlay" id="tutor-notification-overlay">
    <!-- Sidebar -->
    <div class="tutor-notification-sidebar" id="tutor-notification-sidebar">
        <!-- Sidebar Header -->
        <div class="tutor-notification-header">
            <h3 class="tutor-notification-title">
                <svg class="tutor-notification-title-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                    <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                </svg>
                Bildir<PERSON><PERSON>
            </h3>
            <button class="tutor-notification-close" id="tutor-notification-close" aria-label="Bildirimleri Kapat" title="Kapat">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>

        <!-- Sidebar Content -->
        <div class="tutor-notification-content">
            <!-- Bildirim yoksa gösterilecek mesaj -->
            <div class="tutor-notification-empty" id="tutor-notification-empty">
                <div class="tutor-notification-empty-icon">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                        <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
                    </svg>
                </div>
                <h4>Henüz bildirim yok</h4>
                <p>Yeni bildirimleriniz burada görünecek.</p>
            </div>

            <!-- Bildirimler listesi -->
            <div class="tutor-notification-list" id="tutor-notification-list">
                <!-- Yükleme animasyonu -->
                <div class="tutor-notification-loading" id="tutor-notification-loading">
                    <div class="tutor-notification-loading-spinner">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <line x1="12" y1="2" x2="12" y2="6"></line>
                            <line x1="12" y1="18" x2="12" y2="22"></line>
                            <line x1="4.93" y1="4.93" x2="7.76" y2="7.76"></line>
                            <line x1="16.24" y1="16.24" x2="19.07" y2="19.07"></line>
                            <line x1="2" y1="12" x2="6" y2="12"></line>
                            <line x1="18" y1="12" x2="22" y2="12"></line>
                            <line x1="4.93" y1="19.07" x2="7.76" y2="16.24"></line>
                            <line x1="16.24" y1="7.76" x2="19.07" y2="4.93"></line>
                        </svg>
                    </div>
                    <span>Bildirimler yükleniyor...</span>
                </div>

                <!-- Bildirimler buraya dinamik olarak eklenecek -->
                <div class="tutor-notification-items" id="tutor-notification-items">
                    <!-- JavaScript ile doldurulacak -->
                </div>

                <!-- Daha fazla yükle butonu -->
                <div class="tutor-notification-load-more" id="tutor-notification-load-more" style="display: none;">
                    <button class="tutor-notification-load-more-btn" id="tutor-notification-load-more-btn">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="6,9 12,15 18,9"></polyline>
                        </svg>
                        Daha Fazla Yükle
                    </button>
                </div>
            </div>
        </div>

        <!-- Sidebar Footer -->
        <div class="tutor-notification-footer">
            <button class="tutor-notification-footer-btn" id="tutor-notification-mark-all-read" style="display: none;">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <polyline points="20,6 9,17 4,12"></polyline>
                </svg>
                Tümünü Okundu İşaretle
            </button>

            <!-- Bildirim sayısı göstergesi -->
            <div class="tutor-notification-footer-info" id="tutor-notification-footer-info" style="display: none;">
                <span class="tutor-notification-count-text">
                    <span id="tutor-notification-unread-count">0</span> okunmamış bildirim
                </span>
            </div>
        </div>
    </div>
</div>
