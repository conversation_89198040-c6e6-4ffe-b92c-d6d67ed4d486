<?php
/**
 * <PERSON>ildirim Sistemi Sınıfı
 * Bu sınıf, kurs oluşturma bildirimleri için gerekli tüm işlevleri yönetir.
 *
 * @package DmrLMS
 * @since 1.0.7
 */

// <PERSON><PERSON>rudan er<PERSON>im<PERSON> engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Dmr_LMS_Notification_System sınıfı
 * 
 * Kurs oluşturma bildirimleri için veritabanı işlemleri, hook'lar ve AJAX endpoint'leri
 */
class Dmr_LMS_Notification_System {

    /**
     * Singleton instance
     */
    private static $instance = null;

    /**
     * Veritabanı tablo adı
     */
    private $table_name;

    /**
     * Constructor
     */
    private function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'dmr_notifications';
        
        $this->init_hooks();
    }

    /**
     * Singleton instance döndür
     */
    public static function instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Hook'ları başlat
     */
    private function init_hooks() {
        // Eklenti aktif edildiğinde veritabanı tablosunu oluştur
        register_activation_hook(DMR_LMS_FILE, array($this, 'create_notifications_table'));
        
        // Kurs yayınlandığında bildirim oluştur
        add_action('publish_courses', array($this, 'create_course_notification'), 10, 2);
        
        // AJAX endpoint'leri
        add_action('wp_ajax_dmr_get_notifications', array($this, 'ajax_get_notifications'));
        add_action('wp_ajax_dmr_get_notification_count', array($this, 'ajax_get_notification_count'));
        add_action('wp_ajax_dmr_mark_notification_read', array($this, 'ajax_mark_notification_read'));
        add_action('wp_ajax_dmr_mark_all_notifications_read', array($this, 'ajax_mark_all_notifications_read'));
        
        // JavaScript için AJAX URL'lerini ekle
        add_action('wp_enqueue_scripts', array($this, 'localize_notification_script'));
    }

    /**
     * Bildirimler tablosunu oluştur
     */
    public function create_notifications_table() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE {$this->table_name} (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) NOT NULL,
            course_id bigint(20) NOT NULL,
            message text NOT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            is_read tinyint(1) DEFAULT 0,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY course_id (course_id),
            KEY is_read (is_read),
            KEY created_at (created_at)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Kurs yayınlandığında bildirim oluştur
     */
    public function create_course_notification($post_id, $post) {
        // Sadece kurs post type'ı için çalış
        if ($post->post_type !== tutor()->course_post_type) {
            return;
        }

        // Otomatik kaydetme ve revizyon kontrolü
        if (wp_is_post_revision($post_id) || wp_is_post_autosave($post_id)) {
            return;
        }

        // Eski bir kurs güncellenmişse bildirim oluşturma (5 dakika toleransı)
        $post_date = strtotime($post->post_date);
        $modified_date = strtotime($post->post_modified);
        if (($modified_date - $post_date) > 300) { // 5 dakika = 300 saniye
            return;
        }

        // Kurs bilgilerini al
        $course_title = get_the_title($post_id);
        $course_date = get_the_date('d.m.Y', $post_id);

        // Bildirim mesajını oluştur
        $message = sprintf(
            '%s kursu %s tarihinde tanımlandı.',
            $course_title,
            $course_date
        );

        // Tüm kullanıcılara bildirim oluştur
        $this->create_notification_for_all_users($post_id, $message);
    }

    /**
     * Tüm kullanıcılara bildirim oluştur
     */
    private function create_notification_for_all_users($course_id, $message) {
        global $wpdb;

        // Tüm kullanıcıları al (sadece aktif kullanıcılar)
        $users = get_users(array(
            'fields' => 'ID',
            'meta_query' => array(
                array(
                    'key' => 'wp_capabilities',
                    'compare' => 'EXISTS'
                )
            )
        ));

        if (empty($users)) {
            return;
        }

        // Toplu insert için veri hazırla
        $values = array();
        $placeholders = array();

        foreach ($users as $user_id) {
            $values[] = $user_id;
            $values[] = $course_id;
            $values[] = $message;
            $placeholders[] = '(%d, %d, %s)';
        }

        // Toplu insert yap
        $sql = "INSERT INTO {$this->table_name} (user_id, course_id, message) VALUES " . implode(', ', $placeholders);
        $wpdb->query($wpdb->prepare($sql, $values));
    }

    /**
     * Kullanıcının bildirimlerini getir
     */
    public function get_user_notifications($user_id, $limit = 20, $offset = 0) {
        global $wpdb;

        $sql = $wpdb->prepare(
            "SELECT n.*, p.post_title as course_title 
             FROM {$this->table_name} n 
             LEFT JOIN {$wpdb->posts} p ON n.course_id = p.ID 
             WHERE n.user_id = %d 
             ORDER BY n.created_at DESC 
             LIMIT %d OFFSET %d",
            $user_id,
            $limit,
            $offset
        );

        return $wpdb->get_results($sql);
    }

    /**
     * Kullanıcının okunmamış bildirim sayısını getir
     */
    public function get_unread_count($user_id) {
        global $wpdb;

        return (int) $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$this->table_name} WHERE user_id = %d AND is_read = 0",
            $user_id
        ));
    }

    /**
     * Bildirimi okundu olarak işaretle
     */
    public function mark_as_read($notification_id, $user_id) {
        global $wpdb;

        return $wpdb->update(
            $this->table_name,
            array('is_read' => 1),
            array(
                'id' => $notification_id,
                'user_id' => $user_id
            ),
            array('%d'),
            array('%d', '%d')
        );
    }

    /**
     * Tüm bildirimleri okundu olarak işaretle
     */
    public function mark_all_as_read($user_id) {
        global $wpdb;

        return $wpdb->update(
            $this->table_name,
            array('is_read' => 1),
            array('user_id' => $user_id),
            array('%d'),
            array('%d')
        );
    }

    /**
     * AJAX: Bildirimleri getir
     */
    public function ajax_get_notifications() {
        // Güvenlik kontrolü
        if (!is_user_logged_in()) {
            wp_send_json_error('Lütfen giriş yapın');
            return;
        }

        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'dmr_notification_nonce')) {
            wp_send_json_error('Güvenlik kontrolü başarısız');
            return;
        }

        $user_id = get_current_user_id();
        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $per_page = 10;
        $offset = ($page - 1) * $per_page;

        $notifications = $this->get_user_notifications($user_id, $per_page, $offset);
        $unread_count = $this->get_unread_count($user_id);

        wp_send_json_success(array(
            'notifications' => $notifications,
            'unread_count' => $unread_count,
            'has_more' => count($notifications) === $per_page
        ));
    }

    /**
     * AJAX: Bildirim sayısını getir
     */
    public function ajax_get_notification_count() {
        if (!is_user_logged_in()) {
            wp_send_json_error('Lütfen giriş yapın');
            return;
        }

        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'dmr_notification_nonce')) {
            wp_send_json_error('Güvenlik kontrolü başarısız');
            return;
        }

        $user_id = get_current_user_id();
        $count = $this->get_unread_count($user_id);

        wp_send_json_success(array('count' => $count));
    }

    /**
     * AJAX: Bildirimi okundu işaretle
     */
    public function ajax_mark_notification_read() {
        if (!is_user_logged_in()) {
            wp_send_json_error('Lütfen giriş yapın');
            return;
        }

        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'dmr_notification_nonce')) {
            wp_send_json_error('Güvenlik kontrolü başarısız');
            return;
        }

        $notification_id = isset($_POST['notification_id']) ? intval($_POST['notification_id']) : 0;
        if (!$notification_id) {
            wp_send_json_error('Geçersiz bildirim ID');
            return;
        }

        $user_id = get_current_user_id();
        $result = $this->mark_as_read($notification_id, $user_id);

        if ($result !== false) {
            $new_count = $this->get_unread_count($user_id);
            wp_send_json_success(array('unread_count' => $new_count));
        } else {
            wp_send_json_error('Bildirim güncellenemedi');
        }
    }

    /**
     * AJAX: Tüm bildirimleri okundu işaretle
     */
    public function ajax_mark_all_notifications_read() {
        if (!is_user_logged_in()) {
            wp_send_json_error('Lütfen giriş yapın');
            return;
        }

        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['nonce'], 'dmr_notification_nonce')) {
            wp_send_json_error('Güvenlik kontrolü başarısız');
            return;
        }

        $user_id = get_current_user_id();
        $result = $this->mark_all_as_read($user_id);

        if ($result !== false) {
            wp_send_json_success(array('unread_count' => 0));
        } else {
            wp_send_json_error('Bildirimler güncellenemedi');
        }
    }

    /**
     * JavaScript için AJAX verilerini ekle
     */
    public function localize_notification_script() {
        if (wp_script_is('tutor-notification-system', 'enqueued')) {
            wp_localize_script('tutor-notification-system', 'dmr_notification_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('dmr_notification_nonce')
            ));
        }
    }
}

// Sınıfı başlat
Dmr_LMS_Notification_System::instance();
