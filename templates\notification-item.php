<?php
/**
 * <PERSON><PERSON>dir<PERSON> Öğesi Template
 * <PERSON><PERSON> dosya, tek bir bildirim öğesinin HTML yapısını oluşturur.
 *
 * @package DmrLMS
 * @since 1.0.7
 */

// <PERSON><PERSON>rudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Bildirim verilerini kontrol et
if (!isset($notification) || empty($notification)) {
    return;
}

// Tarih formatını düzenle
$created_date = date('d.m.Y H:i', strtotime($notification->created_at));
$time_ago = human_time_diff(strtotime($notification->created_at), current_time('timestamp')) . ' önce';

// Kurs URL'sini oluştur
$course_url = get_permalink($notification->course_id);
$course_title = $notification->course_title ?: get_the_title($notification->course_id);

// Okunmamış bildirimi işaretle
$unread_class = $notification->is_read ? '' : 'unread';
?>

<div class="tutor-notification-item <?php echo esc_attr($unread_class); ?>" data-notification-id="<?php echo esc_attr($notification->id); ?>">
    <!-- Bildirim İkonu -->
    <div class="tutor-notification-item-icon">
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22,4 12,14.01 9,11.01"></polyline>
        </svg>
    </div>

    <!-- Bildirim İçeriği -->
    <div class="tutor-notification-item-content">
        <!-- Bildirim Mesajı -->
        <div class="tutor-notification-item-message">
            <?php echo esc_html($notification->message); ?>
        </div>

        <!-- Kurs Bilgisi -->
        <?php if ($course_title && $course_url): ?>
        <div class="tutor-notification-item-course">
            <a href="<?php echo esc_url($course_url); ?>" class="tutor-notification-course-link" target="_blank">
                <svg class="tutor-notification-course-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                    <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                </svg>
                <span><?php echo esc_html($course_title); ?></span>
                <svg class="tutor-notification-external-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                    <polyline points="15,3 21,3 21,9"></polyline>
                    <line x1="10" y1="14" x2="21" y2="3"></line>
                </svg>
            </a>
        </div>
        <?php endif; ?>

        <!-- Tarih Bilgisi -->
        <div class="tutor-notification-item-time">
            <svg class="tutor-notification-time-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <polyline points="12,6 12,12 16,14"></polyline>
            </svg>
            <span class="tutor-notification-time-text" title="<?php echo esc_attr($created_date); ?>">
                <?php echo esc_html($time_ago); ?>
            </span>
        </div>
    </div>

    <!-- Okundu İşaretleme Butonu (sadece okunmamış bildirimler için) -->
    <?php if (!$notification->is_read): ?>
    <div class="tutor-notification-item-actions">
        <button class="tutor-notification-mark-read" 
                data-notification-id="<?php echo esc_attr($notification->id); ?>"
                title="Okundu İşaretle"
                aria-label="Bu bildirimi okundu olarak işaretle">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="20,6 9,17 4,12"></polyline>
            </svg>
        </button>
    </div>
    <?php endif; ?>
</div>
